================================================================================
代码框架验证报告
================================================================================

验证时间: 2025-06-17 23:54:58
项目根目录: /data2/syd_data/Breakfast_Data/Code

总体评估: 3/5 项检查通过
错误数量: 4
警告数量: 2

详细验证结果:
--------------------------------------------------

file_structure: ✅ 通过

import_dependencies: ✅ 通过

configuration_consistency: ✅ 通过

data_flow_logic: ❌ 失败
  问题: 4 个

output_compliance: ❌ 失败
  问题: 2 个

错误详情:
--------------------
1. 原型训练: 缺少关键类 ['compute_prototypes']
2. 边权训练: 缺少关键类 ['compute_edge_weights']
3. 静态测试: 缺少关键类 ['predict_sequence']
4. 动态测试: 缺少关键类 ['predict_sequence']

警告详情:
--------------------
1. Test/Test_Static.py: 缺少输出目录定义 ['Raw_data', 'Visualization']
2. Test/Test_Dynamic.py: 缺少输出目录定义 ['Raw_data', 'Visualization']

改进建议:
--------------------
1. 修复所有错误项，确保代码框架完整性
2. 检查并解决警告项，提高代码质量

验证完成。
