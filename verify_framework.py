#!/usr/bin/env python3
"""
代码框架验证脚本 - verify_framework.py
验证重构后的代码框架逻辑完整性和一致性，确保符合IDEA.md所有要求

验证内容：
1. 文件结构完整性检查
2. 导入依赖关系验证
3. 配置一致性检查
4. 数据流逻辑验证
5. 输出规范符合性检查
"""

import os
import sys
import ast
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Set
import importlib.util

# 项目根目录
PROJECT_ROOT = Path(__file__).parent

# 预期的文件结构
EXPECTED_FILES = {
    "数据预处理": "txt_to_npy.py",
    "目录设置": "setup_directories.py", 
    "总控脚本": "run_experiment.py",
    "训练脚本": [
        "Train/Train_Action_Prototype.py",
        "Train/Train_Edge_Weight.py", 
        "Train/Train_Static_Model.py",
        "Train/Train_Dynamic_Model.py"
    ],
    "测试脚本": [
        "Test/Test_Static.py",
        "Test/Test_Dynamic.py",
        "Test/Test_Static_vs_Dynamic.py"
    ],
    "数据模块": "src/data/datamodule.py"
}

# IDEA.md规范的关键配置
IDEA_REQUIREMENTS = {
    "数据根目录": "/data2/syd_data/Breakfast_Data",
    "输出基目录": "/data2/syd_data/Breakfast_Data/Outputs",
    "训练数据": ["s1", "s2", "s3"],
    "测试数据": ["s4"],
    "特征维度": 64,
    "时间戳格式": r"\d{8}-\d{6}",
    "Bootstrap次数": 500,
    "随机种子": 42,
    "最大训练轮数": 10
}


class FrameworkVerifier:
    """代码框架验证器"""
    
    def __init__(self):
        self.verification_results = {}
        self.errors = []
        self.warnings = []
        
        print("🔍 代码框架验证器初始化")
        print(f"   - 项目根目录: {PROJECT_ROOT}")
        print(f"   - 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def check_file_structure(self) -> bool:
        """检查文件结构完整性"""
        print("\n📁 检查文件结构完整性...")
        
        missing_files = []
        existing_files = []
        
        for category, files in EXPECTED_FILES.items():
            if isinstance(files, str):
                files = [files]
            
            for file_path in files:
                full_path = PROJECT_ROOT / file_path
                if full_path.exists():
                    existing_files.append(file_path)
                    print(f"   ✅ {file_path}")
                else:
                    missing_files.append(file_path)
                    print(f"   ❌ {file_path}")
        
        if missing_files:
            self.errors.append(f"缺失文件: {', '.join(missing_files)}")
        
        self.verification_results["file_structure"] = {
            "existing_files": existing_files,
            "missing_files": missing_files,
            "complete": len(missing_files) == 0
        }
        
        return len(missing_files) == 0
    
    def check_import_dependencies(self) -> bool:
        """检查导入依赖关系"""
        print("\n📦 检查导入依赖关系...")
        
        import_errors = []
        
        # 检查关键脚本的导入
        key_scripts = [
            "txt_to_npy.py",
            "Train/Train_Action_Prototype.py",
            "Train/Train_Dynamic_Model.py",
            "Test/Test_Static.py"
        ]
        
        for script_path in key_scripts:
            full_path = PROJECT_ROOT / script_path
            if not full_path.exists():
                continue
            
            try:
                # 解析AST检查导入
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                imports = []
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    elif isinstance(node, ast.ImportFrom):
                        if node.module:
                            imports.append(node.module)
                
                print(f"   ✅ {script_path}: {len(imports)} 个导入")
                
            except Exception as e:
                error_msg = f"{script_path}: 导入检查失败 - {e}"
                import_errors.append(error_msg)
                print(f"   ❌ {error_msg}")
        
        if import_errors:
            self.errors.extend(import_errors)
        
        self.verification_results["import_dependencies"] = {
            "errors": import_errors,
            "valid": len(import_errors) == 0
        }
        
        return len(import_errors) == 0
    
    def check_configuration_consistency(self) -> bool:
        """检查配置一致性"""
        print("\n⚙️ 检查配置一致性...")
        
        config_issues = []
        
        # 检查关键配置文件
        scripts_to_check = [
            "txt_to_npy.py",
            "Train/Train_Action_Prototype.py",
            "Test/Test_Static.py"
        ]
        
        for script_path in scripts_to_check:
            full_path = PROJECT_ROOT / script_path
            if not full_path.exists():
                continue
            
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查数据根目录
                if 'DATA_ROOT' in content:
                    if IDEA_REQUIREMENTS["数据根目录"] not in content:
                        config_issues.append(f"{script_path}: DATA_ROOT配置不符合IDEA.md规范")
                
                # 检查随机种子
                if 'RANDOM_SEED' in content:
                    if str(IDEA_REQUIREMENTS["随机种子"]) not in content:
                        config_issues.append(f"{script_path}: RANDOM_SEED配置不符合IDEA.md规范")
                
                # 检查最大训练轮数
                if 'MAX_EPOCHS' in content:
                    if str(IDEA_REQUIREMENTS["最大训练轮数"]) not in content:
                        config_issues.append(f"{script_path}: MAX_EPOCHS配置不符合IDEA.md规范")
                
                print(f"   ✅ {script_path}: 配置检查通过")
                
            except Exception as e:
                error_msg = f"{script_path}: 配置检查失败 - {e}"
                config_issues.append(error_msg)
                print(f"   ❌ {error_msg}")
        
        if config_issues:
            self.warnings.extend(config_issues)
        
        self.verification_results["configuration_consistency"] = {
            "issues": config_issues,
            "consistent": len(config_issues) == 0
        }
        
        return len(config_issues) == 0
    
    def check_data_flow_logic(self) -> bool:
        """检查数据流逻辑"""
        print("\n🔄 检查数据流逻辑...")
        
        logic_issues = []
        
        # 检查数据流的关键步骤
        data_flow_steps = [
            ("数据预处理", "txt_to_npy.py", ["MultiViewFusion", "DataProcessor"]),
            ("原型训练", "Train/Train_Action_Prototype.py", ["ActionPrototypeTrainer"]),
            ("边权训练", "Train/Train_Edge_Weight.py", ["EdgeWeightTrainer"]),
            ("静态训练", "Train/Train_Static_Model.py", ["StaticModel", "StaticModelTrainer"]),
            ("动态训练", "Train/Train_Dynamic_Model.py", ["DynamicModel", "MLPDiff"]),
            ("静态测试", "Test/Test_Static.py", ["StaticModelTester"]),
            ("动态测试", "Test/Test_Dynamic.py", ["DynamicModelTester"])
        ]
        
        for step_name, script_path, required_classes in data_flow_steps:
            full_path = PROJECT_ROOT / script_path
            if not full_path.exists():
                logic_issues.append(f"{step_name}: 脚本文件不存在")
                continue
            
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                missing_classes = []
                for class_name in required_classes:
                    if f"class {class_name}" not in content:
                        missing_classes.append(class_name)
                
                if missing_classes:
                    logic_issues.append(f"{step_name}: 缺少关键类 {missing_classes}")
                else:
                    print(f"   ✅ {step_name}: 逻辑结构完整")
                
            except Exception as e:
                error_msg = f"{step_name}: 逻辑检查失败 - {e}"
                logic_issues.append(error_msg)
                print(f"   ❌ {error_msg}")
        
        if logic_issues:
            self.errors.extend(logic_issues)
        
        self.verification_results["data_flow_logic"] = {
            "issues": logic_issues,
            "valid": len(logic_issues) == 0
        }
        
        return len(logic_issues) == 0
    
    def check_output_compliance(self) -> bool:
        """检查输出规范符合性"""
        print("\n📋 检查输出规范符合性...")
        
        compliance_issues = []
        
        # 检查输出目录结构定义
        training_scripts = [
            "Train/Train_Action_Prototype.py",
            "Train/Train_Edge_Weight.py",
            "Train/Train_Static_Model.py",
            "Train/Train_Dynamic_Model.py"
        ]

        test_scripts = [
            "Test/Test_Static.py",
            "Test/Test_Dynamic.py"
        ]

        # 检查训练脚本（需要完整的输出目录结构）
        for script_path in training_scripts:
            full_path = PROJECT_ROOT / script_path
            if not full_path.exists():
                continue

            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查是否定义了正确的输出目录
                required_dirs = ["Model_parameters", "Raw_data", "Visualization"]
                missing_dirs = []

                for dir_name in required_dirs:
                    if dir_name not in content:
                        missing_dirs.append(dir_name)

                # 检查时间戳ID格式
                if "TIMESTAMP_ID" not in content:
                    compliance_issues.append(f"{script_path}: 缺少时间戳ID定义")

                if missing_dirs:
                    compliance_issues.append(f"{script_path}: 缺少输出目录定义 {missing_dirs}")
                else:
                    print(f"   ✅ {script_path}: 输出规范符合")

            except Exception as e:
                error_msg = f"{script_path}: 输出规范检查失败 - {e}"
                compliance_issues.append(error_msg)
                print(f"   ❌ {error_msg}")

        # 检查测试脚本（只需要基本的输出结构）
        for script_path in test_scripts:
            full_path = PROJECT_ROOT / script_path
            if not full_path.exists():
                continue

            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查时间戳ID格式
                if "TIMESTAMP_ID" not in content:
                    compliance_issues.append(f"{script_path}: 缺少时间戳ID定义")
                else:
                    print(f"   ✅ {script_path}: 输出规范符合")

            except Exception as e:
                error_msg = f"{script_path}: 输出规范检查失败 - {e}"
                compliance_issues.append(error_msg)
                print(f"   ❌ {error_msg}")
        
        if compliance_issues:
            self.warnings.extend(compliance_issues)
        
        self.verification_results["output_compliance"] = {
            "issues": compliance_issues,
            "compliant": len(compliance_issues) == 0
        }
        
        return len(compliance_issues) == 0

    def generate_verification_report(self) -> str:
        """生成验证报告"""
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        report_path = PROJECT_ROOT / f"verification_report_{timestamp}.txt"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("代码框架验证报告\n")
            f.write("=" * 80 + "\n\n")

            f.write(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"项目根目录: {PROJECT_ROOT}\n\n")

            # 总体评估
            total_checks = len(self.verification_results)
            passed_checks = sum(1 for result in self.verification_results.values()
                              if result.get('complete', result.get('valid', result.get('consistent', result.get('compliant', False)))))

            f.write(f"总体评估: {passed_checks}/{total_checks} 项检查通过\n")
            f.write(f"错误数量: {len(self.errors)}\n")
            f.write(f"警告数量: {len(self.warnings)}\n\n")

            # 详细结果
            f.write("详细验证结果:\n")
            f.write("-" * 50 + "\n\n")

            for check_name, result in self.verification_results.items():
                status = "✅ 通过" if result.get('complete', result.get('valid', result.get('consistent', result.get('compliant', False)))) else "❌ 失败"
                f.write(f"{check_name}: {status}\n")

                if 'missing_files' in result and result['missing_files']:
                    f.write(f"  缺失文件: {', '.join(result['missing_files'])}\n")

                if 'errors' in result and result['errors']:
                    f.write(f"  错误: {len(result['errors'])} 个\n")

                if 'issues' in result and result['issues']:
                    f.write(f"  问题: {len(result['issues'])} 个\n")

                f.write("\n")

            # 错误详情
            if self.errors:
                f.write("错误详情:\n")
                f.write("-" * 20 + "\n")
                for i, error in enumerate(self.errors, 1):
                    f.write(f"{i}. {error}\n")
                f.write("\n")

            # 警告详情
            if self.warnings:
                f.write("警告详情:\n")
                f.write("-" * 20 + "\n")
                for i, warning in enumerate(self.warnings, 1):
                    f.write(f"{i}. {warning}\n")
                f.write("\n")

            # 建议
            f.write("改进建议:\n")
            f.write("-" * 20 + "\n")

            if self.errors:
                f.write("1. 修复所有错误项，确保代码框架完整性\n")

            if self.warnings:
                f.write("2. 检查并解决警告项，提高代码质量\n")

            if not self.errors and not self.warnings:
                f.write("代码框架验证完全通过，符合IDEA.md所有要求！\n")

            f.write("\n")
            f.write("验证完成。\n")

        return str(report_path)

    def run_verification(self) -> bool:
        """执行完整的验证流程"""
        print("=" * 80)
        print("🔍 开始代码框架验证")
        print("=" * 80)

        # 执行各项检查
        checks = [
            ("文件结构完整性", self.check_file_structure),
            ("导入依赖关系", self.check_import_dependencies),
            ("配置一致性", self.check_configuration_consistency),
            ("数据流逻辑", self.check_data_flow_logic),
            ("输出规范符合性", self.check_output_compliance)
        ]

        all_passed = True

        for check_name, check_func in checks:
            try:
                result = check_func()
                if not result:
                    all_passed = False
            except Exception as e:
                print(f"   💥 {check_name} 检查异常: {e}")
                self.errors.append(f"{check_name} 检查异常: {e}")
                all_passed = False

        # 生成报告
        print("\n📋 生成验证报告...")
        report_path = self.generate_verification_report()

        # 显示总结
        print("\n" + "=" * 80)
        print("🏁 代码框架验证完成")
        print("=" * 80)

        total_checks = len(self.verification_results)
        passed_checks = sum(1 for result in self.verification_results.values()
                          if result.get('complete', result.get('valid', result.get('consistent', result.get('compliant', False)))))

        print(f"📊 验证统计: {passed_checks}/{total_checks} 项检查通过")
        print(f"❌ 错误数量: {len(self.errors)}")
        print(f"⚠️ 警告数量: {len(self.warnings)}")
        print(f"📋 详细报告: {report_path}")

        if all_passed and not self.errors:
            print("🎉 代码框架验证完全通过，符合IDEA.md所有要求！")
        elif not self.errors:
            print("✅ 代码框架基本通过验证，存在少量警告")
        else:
            print("⚠️ 代码框架存在问题，请查看报告了解详情")

        print("=" * 80)

        return all_passed and not self.errors


def main():
    """主函数"""
    verifier = FrameworkVerifier()
    success = verifier.run_verification()

    # 返回适当的退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
